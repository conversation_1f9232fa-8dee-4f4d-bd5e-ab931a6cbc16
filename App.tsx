
import SearchPage from "./imports/SearchPage";
import WebsitePage from "./imports/WebsitePage";
import CustomerSignInPage from "./imports/CustomerSignInPage";
import { ImageWithFallback } from "./components/figma/ImageWithFallback";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "./components/ui/card";
import { Badge } from "./components/ui/badge";
import { Separator } from "./components/ui/separator";
import { Button } from "./components/ui/button";
import {
  ChevronRight,
  Search,
  Globe,
  LogIn,
  User,
  Shield,
  MessageCircle,
  UserPlus,
  Building2,
  Mail,
  CheckCircle,
  Upload,
  FileText,
  Truck,
  Filter,
  Star,
  DollarSign,
  BarChart3,
  Eye,
  Layout,
  HelpCircle,
} from "lucide-react";

// Import the account creation images from public/assets
const businessProfileImage = "/assets/usinessProfileImage.jpg";
const emailVerificationImage = "/assets/customer Verification page  step 3.jpg";
const registrationCompletedImage = "/assets/Customer Successful page step 4.jpg";

// Import the CFS service provider images from public/assets
const cfsServiceProviderImage = "/assets/Customer CFS(Service Request) page.png";
const requestPricingImage = "/assets/Customer CFS(Pricing Request) page.png";

// Import the website main page image
const websiteMainPageImage = "/assets/website main page.jpg";

// Import the customer sign-in page image
const customerSignInPageImage = "/assets/Customer Sign-in Page.jpg";

// Import the Google search page image
const googleSearchPageImage = "/assets/Customer Sign-in google Autentication Page step 2.jpg";

export default function App() {
  const scrollToStep = (stepId: string) => {
    document
      .getElementById(stepId)
      ?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              How to Use Link My Logistics
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A complete step-by-step guide to help you navigate
              and use our logistics platform effectively
            </p>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center space-x-3 py-4 flex-wrap">
            <Button
              variant="ghost"
              onClick={() => scrollToStep("step1")}
              className="flex items-center space-x-2 text-sm"
            >
              <Search className="w-4 h-4" />
              <span>Search</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => scrollToStep("step2")}
              className="flex items-center space-x-2 text-sm"
            >
              <Globe className="w-4 h-4" />
              <span>Navigate</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => scrollToStep("step3")}
              className="flex items-center space-x-2 text-sm"
            >
              <LogIn className="w-4 h-4" />
              <span>Sign In</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => scrollToStep("account-creation")}
              className="flex items-center space-x-2 text-sm"
            >
              <UserPlus className="w-4 h-4" />
              <span>Create Account</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => scrollToStep("cfs-services")}
              className="flex items-center space-x-2 text-sm"
            >
              <Truck className="w-4 h-4" />
              <span>Explore CFS</span>
            </Button>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Step 1 */}
        <section id="step1" className="mb-16">
          <Card className="overflow-hidden">
            <CardHeader className="bg-blue-600 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-3xl font-bold">
                    Step 1: Search for Our Platform on Google
                  </CardTitle>
                  <p className="text-blue-100 mt-2">
                    Find and visit our website easily
                  </p>
                </div>
                <Badge
                  variant="secondary"
                  className="bg-blue-500 text-white"
                >
                  <Search className="w-4 h-4 mr-1" />
                  Search
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="grid lg:grid-cols-2 gap-8 p-6">
                <div>
                  <div className="h-[500px] bg-gray-100 rounded-lg overflow-hidden shadow-inner">
                    <ImageWithFallback
                      src={googleSearchPageImage}
                      alt="Google Search Page - Search for Our Platform"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-4 flex items-center">
                      <span className="bg-blue-100 text-blue-600 rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        1
                      </span>
                      Instructions
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          📝 Open your browser and go to
                          Google.com
                        </p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          🔍 In the search bar, type:
                        </p>
                        <code className="bg-blue-100 text-blue-800 px-3 py-1 rounded font-mono">
                          linkmylogistics.com
                        </code>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          ➡ Press Enter to find and visit our
                          website
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                    <p className="text-green-800">
                      <strong>✅ Why this helps:</strong> This
                      helps new users find us easily without
                      typing the full URL.
                    </p>
                  </div>
                  <Button
                    onClick={() => scrollToStep("step2")}
                    className="w-full"
                  >
                    Continue to Step 2{" "}
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        <Separator className="my-12" />

        {/* Step 2 */}
        <section id="step2" className="mb-16">
          <Card className="overflow-hidden">
            <CardHeader className="bg-indigo-600 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-3xl font-bold">
                    Step 2: Navigate the Home Page & Choose
                    Services
                  </CardTitle>
                  <p className="text-indigo-100 mt-2">
                    Explore features and select the services you
                    need
                  </p>
                </div>
                <Badge
                  variant="secondary"
                  className="bg-indigo-500 text-white"
                >
                  <Globe className="w-4 h-4 mr-1" />
                  Navigate
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="grid lg:grid-cols-2 gap-8 p-6">
                <div>
                  <div className="h-[500px] bg-gray-100 rounded-lg overflow-hidden shadow-inner">
                    <ImageWithFallback
                      src={websiteMainPageImage}
                      alt="Website Main Page - Navigate and Choose Services"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-4 flex items-center">
                      <span className="bg-indigo-100 text-indigo-600 rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        2
                      </span>
                      Available Features
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          🔍 <strong>Search by location</strong>{" "}
                          to find CFS (Container Freight
                          Station) providers
                        </p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          💰 <strong>Set your budget</strong>{" "}
                          and storage preferences
                        </p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          🚛{" "}
                          <strong>
                            Choose logistics services:
                          </strong>{" "}
                          CFS, Transport, 3PL, Warehouse
                        </p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          💬 <strong>Ask questions</strong>{" "}
                          through our chatbot assistant
                        </p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          📌{" "}
                          <strong>
                            Click "Search CFS Now"
                          </strong>{" "}
                          to view available providers
                        </p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">
                      👥 Login Options at the Top:
                    </h4>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-blue-50 border border-blue-200 p-3 rounded-lg text-center">
                        <User className="w-6 h-6 mx-auto mb-2 text-blue-600" />
                        <p className="text-sm font-medium">
                          "Sign In" for CHA
                        </p>
                      </div>
                      <div className="bg-green-50 border border-green-200 p-3 rounded-lg text-center">
                        <User className="w-6 h-6 mx-auto mb-2 text-green-600" />
                        <p className="text-sm font-medium">
                          "Sign In" for CFS users
                        </p>
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={() => scrollToStep("step3")}
                    className="w-full"
                  >
                    Continue to Step 3{" "}
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        <Separator className="my-12" />

        {/* Step 3 */}
        <section id="step3" className="mb-16">
          <Card className="overflow-hidden">
            <CardHeader className="bg-purple-600 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-3xl font-bold">
                    Step 3: Log In or Sign Up to Your Account
                  </CardTitle>
                  <p className="text-purple-100 mt-2">
                    Access your dashboard with secure
                    authentication
                  </p>
                </div>
                <Badge
                  variant="secondary"
                  className="bg-purple-500 text-white"
                >
                  <LogIn className="w-4 h-4 mr-1" />
                  Sign In
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="grid lg:grid-cols-2 gap-8 p-6">
                <div>
                  <div className="h-[500px] bg-gray-100 rounded-lg overflow-hidden shadow-inner">
                    <ImageWithFallback
                      src={customerSignInPageImage}
                      alt="Customer Sign-in Page - Log In or Sign Up"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-4 flex items-center">
                      <span className="bg-purple-100 text-purple-600 rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        3
                      </span>
                      Sign In Options
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-3">
                          🔐{" "}
                          <strong>
                            Use your login credentials to access
                            your dashboard:
                          </strong>
                        </p>
                        <ul className="space-y-2 ml-4 text-gray-700">
                          <li>📧 Enter Email or Username</li>
                          <li>🔑 Enter your Password</li>
                          <li>➡ Click Login</li>
                        </ul>
                      </div>
                      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                        <p className="font-medium text-blue-900 mb-2">
                          <Shield className="w-5 h-5 inline mr-2" />
                          <strong>Alternative Login:</strong>
                        </p>
                        <p className="text-blue-800">
                          🔗 Login with Google (OAuth) -
                          Authenticates using Google OAuth for
                          secure access
                        </p>
                      </div>
                      <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                        <p className="font-medium text-green-900 mb-2">
                          🆕{" "}
                          <strong>
                            Don't have an account?
                          </strong>
                        </p>
                        <p className="text-green-800">
                          Click Sign Up to create a new one -
                          Redirects to account creation page
                          (for new customers)
                        </p>
                      </div>
                      <div className="bg-orange-50 border border-orange-200 p-4 rounded-lg">
                        <p className="font-medium text-orange-900">
                          For support, use the email link
                          provided on the login page
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 p-4 rounded-lg">
                    <p className="text-purple-800">
                      <strong>🎉 Success!</strong> Once logged
                      in, you'll have access to your
                      personalized dashboard with all logistics
                      tools and services.
                    </p>
                  </div>
                  <Button
                    onClick={() =>
                      scrollToStep("account-creation")
                    }
                    className="w-full"
                  >
                    Learn How to Create Account{" "}
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        <Separator className="my-12" />

        {/* Account Creation Section */}
        <section id="account-creation" className="mb-16">
          <Card className="overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-green-600 to-emerald-600 text-white">
              <div className="text-center">
                <CardTitle className="text-4xl font-bold mb-2">
                  How to Create an Account on Link My Logistics
                </CardTitle>
                <p className="text-green-100 text-lg">
                  Complete registration process in 4 simple
                  steps
                </p>
              </div>
            </CardHeader>
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="text-center">
                  <div className="bg-blue-100 text-blue-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <UserPlus className="w-6 h-6" />
                  </div>
                  <h4 className="font-semibold">
                    Registration
                  </h4>
                  <p className="text-sm text-gray-600">
                    Sign up with email
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-indigo-100 text-indigo-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Building2 className="w-6 h-6" />
                  </div>
                  <h4 className="font-semibold">
                    Business Profile
                  </h4>
                  <p className="text-sm text-gray-600">
                    Fill company details
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-purple-100 text-purple-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Mail className="w-6 h-6" />
                  </div>
                  <h4 className="font-semibold">
                    Email Verification
                  </h4>
                  <p className="text-sm text-gray-600">
                    Verify your email
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-green-100 text-green-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <CheckCircle className="w-6 h-6" />
                  </div>
                  <h4 className="font-semibold">Completed</h4>
                  <p className="text-sm text-gray-600">
                    Account ready
                  </p>
                </div>
              </div>

              {/* Step 1: Registration */}
              <div className="mb-12">
                <Card className="border-2 border-blue-200">
                  <CardHeader className="bg-blue-50">
                    <CardTitle className="flex items-center text-blue-800">
                      <span className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        1
                      </span>
                      ✅ Step 1: Registration (Sign-In)
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Access the Registration Page:
                        </h4>
                        <p className="text-gray-700">
                          Visit:{" "}
                          <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                            linkmylogistics.com/customer/register/profile
                          </code>
                        </p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Action:
                        </h4>
                        <ul className="space-y-2 text-gray-700 ml-4">
                          <li>
                            • Enter your email address and
                            password to sign up.
                          </li>
                          <li>
                            • Click "Register" to proceed.
                          </li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Step 2: Fill Business Profile */}
              <div className="mb-12">
                <Card className="border-2 border-indigo-200">
                  <CardHeader className="bg-indigo-50">
                    <CardTitle className="flex items-center text-indigo-800">
                      <span className="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        2
                      </span>
                      🏢 Step 2: Fill Business Profile
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid lg:grid-cols-2 gap-8">
                      <div>
                        <div className="h-[400px] bg-gray-100 rounded-lg overflow-hidden shadow-inner">
                          <ImageWithFallback
                            src={businessProfileImage}
                            alt="Business Profile Form"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="space-y-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">
                            Fields to Complete:
                          </h4>
                          <div className="space-y-3">
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <Building2 className="w-5 h-5 mr-3 text-indigo-600" />
                              <span>
                                <strong>
                                  Company/Business Name
                                </strong>{" "}
                                (Required)
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <FileText className="w-5 h-5 mr-3 text-indigo-600" />
                              <span>
                                <strong>Business Type</strong>{" "}
                                (Dropdown) (Required)
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <Globe className="w-5 h-5 mr-3 text-indigo-600" />
                              <span>
                                <strong>
                                  Business Address
                                </strong>{" "}
                                (Required)
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <MessageCircle className="w-5 h-5 mr-3 text-gray-500" />
                              <span>
                                <strong>
                                  Business Description
                                </strong>{" "}
                                (Optional)
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <Upload className="w-5 h-5 mr-3 text-gray-500" />
                              <span>
                                <strong>
                                  Upload Business Documents
                                </strong>{" "}
                                (Optional) — PDF, JPG, PNG, max
                                10MB
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="bg-indigo-50 border border-indigo-200 p-4 rounded-lg">
                          <h4 className="font-semibold text-indigo-900 mb-2">
                            Action:
                          </h4>
                          <ul className="space-y-1 text-indigo-800">
                            <li>
                              • After filling the required info,
                              click "Continue →"
                            </li>
                            <li>
                              • You can choose "Skip for Now" to
                              proceed without documents
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Step 3: Email Verification */}
              <div className="mb-12">
                <Card className="border-2 border-purple-200">
                  <CardHeader className="bg-purple-50">
                    <CardTitle className="flex items-center text-purple-800">
                      <span className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        3
                      </span>
                      📧 Step 3: Email Verification
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid lg:grid-cols-2 gap-8">
                      <div>
                        <div className="h-[400px] bg-gray-100 rounded-lg overflow-hidden shadow-inner">
                          <ImageWithFallback
                            src={emailVerificationImage}
                            alt="Email Verification Screen"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="space-y-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">
                            What Happens:
                          </h4>
                          <div className="space-y-3">
                            <div className="bg-gray-50 p-4 rounded-lg">
                              <p className="flex items-center">
                                <Mail className="w-5 h-5 mr-3 text-purple-600" />
                                You will receive an email to
                                verify your address.
                              </p>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg">
                              <p className="flex items-center">
                                <CheckCircle className="w-5 h-5 mr-3 text-green-600" />
                                Once verified, the screen will
                                display "Email Verified"
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="bg-purple-50 border border-purple-200 p-4 rounded-lg">
                          <h4 className="font-semibold text-purple-900 mb-2">
                            Action:
                          </h4>
                          <p className="text-purple-800">
                            Click "Continue →" to proceed to the
                            final step
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Step 4: Completed */}
              <div className="mb-8">
                <Card className="border-2 border-green-200">
                  <CardHeader className="bg-green-50">
                    <CardTitle className="flex items-center text-green-800">
                      <span className="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        4
                      </span>
                      🎉 Step 4: Completed
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid lg:grid-cols-2 gap-8">
                      <div>
                        <div className="h-[400px] bg-gray-100 rounded-lg overflow-hidden shadow-inner">
                          <ImageWithFallback
                            src={registrationCompletedImage}
                            alt="Registration Completed Screen"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="space-y-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">
                            Confirmation Screen:
                          </h4>
                          <div className="space-y-3">
                            <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                              <p className="text-green-800">
                                <CheckCircle className="w-5 h-5 inline mr-2" />
                                You will see a message
                                confirming your account has been
                                successfully created and
                                verified.
                              </p>
                            </div>
                            <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                              <p className="text-green-800">
                                <Shield className="w-5 h-5 inline mr-2" />
                                You are now ready to explore the
                                customer dashboard and begin
                                using the logistics services.
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gradient-to-r from-green-100 to-emerald-100 border border-green-200 p-6 rounded-lg text-center">
                          <h4 className="font-bold text-green-900 mb-2">
                            🎊 Welcome Aboard!
                          </h4>
                          <p className="text-green-800">
                            Your Link My Logistics account is
                            now ready to use. Start exploring
                            our comprehensive logistics
                            platform!
                          </p>
                        </div>
                        <Button
                          onClick={() =>
                            scrollToStep("cfs-services")
                          }
                          className="w-full"
                        >
                          Learn How to Explore CFS Services{" "}
                          <ChevronRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </section>

        <Separator className="my-12" />

        {/* CFS Services Section */}
        <section id="cfs-services" className="mb-16">
          <Card className="overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white">
              <div className="text-center">
                <CardTitle className="text-4xl font-bold mb-2">
                  How to Explore CFS Service Provider and
                  Request Pricing for Solutions
                </CardTitle>
                <p className="text-orange-100 text-lg">
                  Complete guide to finding and requesting
                  quotes from service providers
                </p>
              </div>
            </CardHeader>
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="bg-blue-100 text-blue-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Truck className="w-6 h-6" />
                  </div>
                  <h4 className="font-semibold">
                    Choose Service
                  </h4>
                  <p className="text-sm text-gray-600">
                    Select logistics category
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-indigo-100 text-indigo-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Filter className="w-6 h-6" />
                  </div>
                  <h4 className="font-semibold">
                    Apply Filters
                  </h4>
                  <p className="text-sm text-gray-600">
                    Narrow search results
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-purple-100 text-purple-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Star className="w-6 h-6" />
                  </div>
                  <h4 className="font-semibold">
                    Browse Providers
                  </h4>
                  <p className="text-sm text-gray-600">
                    View provider cards
                  </p>
                </div>
              </div>

              {/* Step 1: Choose the Logistics Service */}
              <div className="mb-12">
                <Card className="border-2 border-blue-200">
                  <CardHeader className="bg-blue-50">
                    <CardTitle className="flex items-center text-blue-800">
                      <span className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        1
                      </span>
                      ✅ Step 1: Choose the Logistics Service
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-3">
                          On the homepage, you will see buttons
                          for various logistics categories:
                        </p>
                        <div className="grid grid-cols-5 gap-3">
                          <div className="bg-blue-100 text-blue-800 px-3 py-2 rounded text-center font-medium">
                            CFS
                          </div>
                          <div className="bg-green-100 text-green-800 px-3 py-2 rounded text-center font-medium">
                            Transport
                          </div>
                          <div className="bg-purple-100 text-purple-800 px-3 py-2 rounded text-center font-medium">
                            3PL
                          </div>
                          <div className="bg-orange-100 text-orange-800 px-3 py-2 rounded text-center font-medium">
                            Warehouse
                          </div>
                          <div className="bg-gray-100 text-gray-800 px-3 py-2 rounded text-center font-medium">
                            Custom
                          </div>
                        </div>
                      </div>
                      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                        <p className="text-blue-800">
                          <strong>👉 Action:</strong> Click the
                          service type you are looking for
                          (e.g., CFS).
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Step 2: Use Filters to Narrow Results */}
              <div className="mb-12">
                <Card className="border-2 border-indigo-200">
                  <CardHeader className="bg-indigo-50">
                    <CardTitle className="flex items-center text-indigo-800">
                      <span className="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        2
                      </span>
                      🔍 Step 2: Use Filters to Narrow Results
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-3">
                          Use the Search & Filter Bar on the
                          top-right:
                        </p>
                        <ul className="space-y-2 ml-4 text-gray-700">
                          <li>
                            • Search for specific service
                            providers
                          </li>
                          <li>
                            • Apply filters like location,
                            service type, etc.
                          </li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Step 3: Browse Service Provider Cards */}
              <div className="mb-12">
                <Card className="border-2 border-purple-200">
                  <CardHeader className="bg-purple-50">
                    <CardTitle className="flex items-center text-purple-800">
                      <span className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        3
                      </span>
                      📇 Step 3: Browse Service Provider Cards
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid lg:grid-cols-2 gap-8">
                      <div>
                        <div className="h-[400px] bg-gray-100 rounded-lg overflow-hidden shadow-inner">
                          <ImageWithFallback
                            src={cfsServiceProviderImage}
                            alt="CFS Service Provider Listing"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="space-y-6">
                        <div>
                          <p className="font-medium text-gray-900 mb-3">
                            Scroll through the listed Service
                            Provider Cards.
                          </p>
                          <p className="font-medium text-gray-900 mb-3">
                            Each card displays:
                          </p>
                          <div className="space-y-3">
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <Globe className="w-5 h-5 mr-3 text-purple-600" />
                              <span>
                                <strong>📍 Location</strong>
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <Truck className="w-5 h-5 mr-3 text-purple-600" />
                              <span>
                                <strong>🏷 Services</strong>{" "}
                                (e.g., Packing, Storage,
                                Customs)
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <Star className="w-5 h-5 mr-3 text-purple-600" />
                              <span>
                                <strong>⭐ Ratings</strong>
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <FileText className="w-5 h-5 mr-3 text-purple-600" />
                              <span>
                                <strong>📄 Description</strong>
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Step 4: Request a Pricing Quote */}
              <div className="mb-12">
                <Card className="border-2 border-green-200">
                  <CardHeader className="bg-green-50">
                    <CardTitle className="flex items-center text-green-800">
                      <span className="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        4
                      </span>
                      📦 Step 4: Request a Pricing Quote
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid lg:grid-cols-2 gap-8">
                      <div>
                        <div className="h-[400px] bg-gray-100 rounded-lg overflow-hidden shadow-inner">
                          <ImageWithFallback
                            src={requestPricingImage}
                            alt="Request Pricing Form"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="space-y-6">
                        <div>
                          <p className="font-medium text-gray-900 mb-3">
                            Click the "Request Price" button on
                            the provider card.
                          </p>
                          <p className="font-medium text-gray-900 mb-3">
                            A form will appear with the
                            following fields:
                          </p>
                          <div className="space-y-3">
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <FileText className="w-5 h-5 mr-3 text-green-600" />
                              <span>
                                <strong>DPD / Non-DPD</strong>
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <Building2 className="w-5 h-5 mr-3 text-green-600" />
                              <span>
                                <strong>Container Type</strong>
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <DollarSign className="w-5 h-5 mr-3 text-green-600" />
                              <span>
                                <strong>Preferable Rate</strong>
                              </span>
                            </div>
                            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                              <BarChart3 className="w-5 h-5 mr-3 text-green-600" />
                              <span>
                                <strong>
                                  No. of Container Movements per
                                  Month
                                </strong>
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                          <h4 className="font-semibold text-green-900 mb-2">
                            Actions:
                          </h4>
                          <ul className="space-y-1 text-green-800">
                            <li>
                              📝 Fill in the required details.
                            </li>
                            <li>✅ Click "Request Pricing".</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Step 5: Track Your Request */}
              <div className="mb-12">
                <Card className="border-2 border-orange-200">
                  <CardHeader className="bg-orange-50">
                    <CardTitle className="flex items-center text-orange-800">
                      <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        5
                      </span>
                      📈 Step 5: Track Your Request
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          Once submitted, your request
                          automatically appears in your
                          dashboard under:
                        </p>
                        <div className="bg-orange-100 text-orange-800 px-3 py-2 rounded font-mono text-center mt-2">
                          CFS &gt; Pricing Requests table
                        </div>
                      </div>
                      <div className="bg-orange-50 border border-orange-200 p-4 rounded-lg">
                        <p className="text-orange-800">
                          You can view the status and manage
                          responses from there.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Step 6: View More Details */}
              <div className="mb-12">
                <Card className="border-2 border-red-200">
                  <CardHeader className="bg-red-50">
                    <CardTitle className="flex items-center text-red-800">
                      <span className="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-bold">
                        6
                      </span>
                      📊 Step 6: View More Details (Optional)
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-3">
                          Click "View Details" on any card to
                          open a dedicated page with:
                        </p>
                        <div className="grid grid-cols-2 gap-3">
                          <div className="flex items-center p-3 bg-white border rounded-lg">
                            <Eye className="w-5 h-5 mr-3 text-red-600" />
                            <span>More photos</span>
                          </div>
                          <div className="flex items-center p-3 bg-white border rounded-lg">
                            <FileText className="w-5 h-5 mr-3 text-red-600" />
                            <span>Service details</span>
                          </div>
                          <div className="flex items-center p-3 bg-white border rounded-lg">
                            <Building2 className="w-5 h-5 mr-3 text-red-600" />
                            <span>Facilities offered</span>
                          </div>
                          <div className="flex items-center p-3 bg-white border rounded-lg">
                            <MessageCircle className="w-5 h-5 mr-3 text-red-600" />
                            <span>Contact options</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Additional Features */}
              <div className="grid md:grid-cols-2 gap-8 mb-8">
                <Card className="border-2 border-gray-200">
                  <CardHeader className="bg-gray-50">
                    <CardTitle className="flex items-center text-gray-800">
                      <Layout className="w-6 h-6 mr-3" />
                      🔁 Access Dashboard Anytime
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <p className="font-medium text-gray-900">
                        Click on the Dashboard button
                        (top-right) to:
                      </p>
                      <div className="space-y-2">
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-gray-700">
                            • Manage all service requests
                          </p>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-gray-700">
                            • View quotes, approvals, and
                            messages
                          </p>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-gray-700">
                            • Track ongoing logistics operations
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-2 border-blue-200">
                  <CardHeader className="bg-blue-50">
                    <CardTitle className="flex items-center text-blue-800">
                      <HelpCircle className="w-6 h-6 mr-3" />
                      💬 Need Help?
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg text-center">
                        <MessageCircle className="w-12 h-12 mx-auto mb-3 text-blue-600" />
                        <p className="text-blue-800 font-medium">
                          Use the live chat popup in the
                          bottom-right corner for assistance
                          anytime.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </section>

        <Separator className="my-12" />

        {/* Additional Features */}
        <section className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">
                Additional Platform Features
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-blue-50 rounded-lg">
                  <MessageCircle className="w-12 h-12 mx-auto mb-4 text-blue-600" />
                  <h3 className="font-semibold mb-2">
                    24/7 Chatbot Support
                  </h3>
                  <p className="text-gray-600">
                    Get instant help with our intelligent
                    chatbot assistant available around the
                    clock.
                  </p>
                </div>
                <div className="text-center p-6 bg-green-50 rounded-lg">
                  <Search className="w-12 h-12 mx-auto mb-4 text-green-600" />
                  <h3 className="font-semibold mb-2">
                    Smart CFS Search
                  </h3>
                  <p className="text-gray-600">
                    Find the perfect Container Freight Station
                    based on location, budget, and requirements.
                  </p>
                </div>
                <div className="text-center p-6 bg-purple-50 rounded-lg">
                  <Shield className="w-12 h-12 mx-auto mb-4 text-purple-600" />
                  <h3 className="font-semibold mb-2">
                    Secure Authentication
                  </h3>
                  <p className="text-gray-600">
                    Multiple secure login options including
                    Google OAuth for enhanced security.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Call to Action */}
        <section className="text-center">
          <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold mb-4">
                Ready to Get Started?
              </h2>
              <p className="text-xl mb-6 text-blue-100">
                Follow these simple steps to start using Link My
                Logistics today!
              </p>
              <Button
                size="lg"
                variant="secondary"
                onClick={() => scrollToStep("step1")}
                className="mr-4"
              >
                Start with Step 1
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="bg-transparent border-white text-white hover:bg-white hover:text-purple-600"
              >
                Visit Website
              </Button>
            </CardContent>
          </Card>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2025 Link My Logistics. All rights reserved. |
            Need help? Contact our support team.
          </p>
        </div>
      </footer>
    </div>
  );
}
